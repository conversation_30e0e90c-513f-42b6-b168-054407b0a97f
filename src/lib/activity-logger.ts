import { query } from '@/lib/local-db'

export type ActivityEventType =
  | 'company_added'
  | 'company_deleted'
  | 'user_registered'
  | 'user_deleted'
  | 'benefit_verified'
  | 'benefit_disputed'
  | 'benefit_removal_dispute_submitted'
  | 'benefit_removal_dispute_approved'
  | 'benefit_removal_dispute_rejected'
  | 'benefit_removal_dispute_cancelled'
  | 'benefit_automatically_removed'
  | 'cache_refresh'
  | 'session_cleanup'

export interface ActivityLogEntry {
  id: string
  event_type: ActivityEventType
  event_description: string
  user_id?: string
  user_email?: string
  user_name?: string
  company_id?: string
  company_name?: string
  benefit_id?: string
  benefit_name?: string
  metadata?: Record<string, unknown>
  created_at: string
}

export interface LogActivityParams {
  eventType: ActivityEventType
  description: string
  userId?: string
  userEmail?: string
  userName?: string
  companyId?: string
  companyName?: string
  benefitId?: string
  benefitName?: string
  metadata?: Record<string, unknown>
}

/**
 * Log an activity to the activity log table
 */
export async function logActivity(params: LogActivityParams): Promise<void> {
  try {
    await query(
      `INSERT INTO activity_log (
        event_type, event_description, user_id, user_email, user_name,
        company_id, company_name, benefit_id, benefit_name, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
      [
        params.eventType,
        params.description,
        params.userId || null,
        params.userEmail || null,
        params.userName || null,
        params.companyId || null,
        params.companyName || null,
        params.benefitId || null,
        params.benefitName || null,
        params.metadata ? JSON.stringify(params.metadata) : null
      ]
    )
    console.log(`✅ Activity logged: ${params.eventType} - ${params.description}`)
  } catch (error) {
    console.error('Error logging activity:', error)
    // Don't throw error to avoid breaking the main operation
  }
}

/**
 * Log a company addition activity
 */
export async function logCompanyAdded(
  companyId: string,
  companyName: string,
  userId?: string,
  userEmail?: string,
  userName?: string
): Promise<void> {
  await logActivity({
    eventType: 'company_added',
    description: `New company added: ${companyName}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    metadata: {
      source: userId ? 'admin_dashboard' : 'system'
    }
  })
}

/**
 * Log a company deletion activity
 */
export async function logCompanyDeleted(
  companyId: string,
  companyName: string,
  adminUserId: string,
  adminUserEmail: string,
  adminUserName?: string
): Promise<void> {
  await logActivity({
    eventType: 'company_deleted',
    description: `Company deleted: ${companyName} (deleted by ${adminUserName || adminUserEmail})`,
    userId: adminUserId,
    userEmail: adminUserEmail,
    userName: adminUserName,
    // Don't pass companyId as it no longer exists in the companies table
    companyName,
    metadata: {
      deleted_company_id: companyId, // Store the deleted company ID in metadata instead
      action_type: 'admin_deletion',
      source: 'admin_dashboard'
    }
  })
}

/**
 * Log a user registration activity
 */
export async function logUserRegistered(
  userId: string,
  userEmail: string,
  userName?: string,
  companyName?: string
): Promise<void> {
  await logActivity({
    eventType: 'user_registered',
    description: `New user registered: ${userName || userEmail}${companyName ? ` at ${companyName}` : ''}`,
    userId,
    userEmail,
    userName,
    companyName,
    metadata: {
      registration_method: 'magic_link'
    }
  })
}

/**
 * Log a user deletion activity
 */
export async function logUserDeleted(
  deletedUserId: string,
  deletedUserEmail: string,
  adminUserId: string,
  adminUserEmail: string,
  deletedUserName?: string,
  adminUserName?: string,
  companyName?: string
): Promise<void> {
  await logActivity({
    eventType: 'user_deleted',
    description: `User deleted: ${deletedUserName || deletedUserEmail}${companyName ? ` at ${companyName}` : ''} (deleted by ${adminUserName || adminUserEmail})`,
    userId: adminUserId,
    userEmail: adminUserEmail,
    userName: adminUserName,
    companyName,
    metadata: {
      deleted_user_id: deletedUserId,
      deleted_user_email: deletedUserEmail,
      deleted_user_name: deletedUserName,
      action_type: 'admin_deletion'
    }
  })
}

/**
 * Log a benefit verification activity
 */
export async function logBenefitVerified(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_verified',
    description: `Benefit verified: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      verification_type: 'user_confirmation'
    }
  })
}

/**
 * Log a benefit dispute activity
 */
export async function logBenefitDisputed(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string,
  comment?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_disputed',
    description: `Benefit disputed: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      dispute_comment: comment,
      verification_type: 'user_dispute'
    }
  })
}

/**
 * Log a benefit removal dispute submission
 */
export async function logBenefitRemovalDisputeSubmitted(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string,
  reason?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_removal_dispute_submitted',
    description: `Benefit removal dispute submitted: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      dispute_reason: reason,
      action_type: 'dispute_submission'
    }
  })
}

/**
 * Log a benefit removal dispute approval/rejection
 */
export async function logBenefitRemovalDisputeProcessed(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  adminUserId: string,
  adminUserEmail: string,
  action: 'approved' | 'rejected',
  disputeUserId: string,
  disputeUserEmail: string,
  adminUserName?: string,
  disputeUserName?: string,
  adminComment?: string
): Promise<void> {
  await logActivity({
    eventType: `benefit_removal_dispute_${action}`,
    description: `Benefit removal dispute ${action}: ${benefitName} at ${companyName} (disputed by ${disputeUserName || disputeUserEmail}, ${action} by ${adminUserName || adminUserEmail})`,
    userId: adminUserId,
    userEmail: adminUserEmail,
    userName: adminUserName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      admin_comment: adminComment,
      dispute_user_id: disputeUserId,
      dispute_user_email: disputeUserEmail,
      dispute_user_name: disputeUserName,
      action_type: `dispute_${action}`
    }
  })
}

/**
 * Log a benefit removal dispute cancellation
 */
export async function logBenefitRemovalDisputeCancelled(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  userId: string,
  userEmail: string,
  userName?: string,
  reason?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_removal_dispute_cancelled',
    description: `Benefit removal dispute cancelled: ${benefitName} at ${companyName} by ${userName || userEmail}`,
    userId,
    userEmail,
    userName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      original_dispute_reason: reason,
      action_type: 'dispute_cancellation'
    }
  })
}

/**
 * Log automatic benefit removal due to approved disputes
 */
export async function logBenefitAutomaticallyRemoved(
  benefitId: string,
  benefitName: string,
  companyId: string,
  companyName: string,
  approvedDisputesCount: number,
  uniqueUserCount: number,
  adminUserId: string,
  adminUserEmail: string,
  adminUserName?: string
): Promise<void> {
  await logActivity({
    eventType: 'benefit_automatically_removed',
    description: `Benefit automatically removed: ${benefitName} at ${companyName} due to ${approvedDisputesCount} approved disputes from ${uniqueUserCount} users`,
    userId: adminUserId,
    userEmail: adminUserEmail,
    userName: adminUserName,
    companyId,
    companyName,
    benefitId,
    benefitName,
    metadata: {
      approved_disputes_count: approvedDisputesCount,
      unique_user_count: uniqueUserCount,
      action_type: 'automatic_removal',
      removal_trigger: 'approved_disputes_threshold'
    }
  })
}

/**
 * Get recent activities for admin dashboard
 */
export async function getRecentActivities(limit: number = 10): Promise<ActivityLogEntry[]> {
  try {
    const result = await query(
      `SELECT
        id, event_type, event_description, user_id, user_email, user_name,
        company_id, company_name, benefit_id, benefit_name, metadata, created_at
      FROM activity_log
      ORDER BY created_at DESC
      LIMIT $1`,
      [limit]
    )

    return result.rows.map(row => ({
      ...row,
      metadata: row.metadata && typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata
    }))
  } catch (error) {
    console.error('Error fetching recent activities:', error)
    return []
  }
}

export interface GetAllActivitiesParams {
  page?: number
  limit?: number
  eventType?: string
  search?: string
  startDate?: string
  endDate?: string
}

export interface GetAllActivitiesResult {
  activities: ActivityLogEntry[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all activities with filtering and pagination for admin dashboard
 */
export async function getAllActivities(params: GetAllActivitiesParams = {}): Promise<GetAllActivitiesResult> {
  try {
    const {
      page = 1,
      limit = 50,
      eventType,
      search,
      startDate,
      endDate
    } = params

    const offset = (page - 1) * limit
    const conditions: string[] = []
    const queryParams: unknown[] = []
    let paramIndex = 1

    // Event type filter
    if (eventType && eventType !== 'all') {
      conditions.push(`event_type = $${paramIndex}`)
      queryParams.push(eventType)
      paramIndex++
    }

    // Search filter (user, company, benefit)
    if (search) {
      conditions.push(`(
        user_email ILIKE $${paramIndex} OR
        user_name ILIKE $${paramIndex} OR
        company_name ILIKE $${paramIndex} OR
        benefit_name ILIKE $${paramIndex} OR
        event_description ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    // Date range filters
    if (startDate) {
      conditions.push(`created_at >= $${paramIndex}`)
      queryParams.push(startDate)
      paramIndex++
    }

    if (endDate) {
      conditions.push(`created_at <= $${paramIndex}`)
      queryParams.push(endDate)
      paramIndex++
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // Get activities with pagination
    const activitiesQuery = `
      SELECT
        id, event_type, event_description, user_id, user_email, user_name,
        company_id, company_name, benefit_id, benefit_name, metadata, created_at
      FROM activity_log
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const activitiesResult = await query(activitiesQuery, queryParams)

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM activity_log
      ${whereClause}
    `

    const countResult = await query(countQuery, queryParams.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)

    const activities = activitiesResult.rows.map(row => ({
      ...row,
      metadata: row.metadata && typeof row.metadata === 'string' ? JSON.parse(row.metadata) : row.metadata
    }))

    return {
      activities,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Error fetching all activities:', error)
    return {
      activities: [],
      pagination: {
        page: 1,
        limit: 50,
        total: 0,
        totalPages: 0
      }
    }
  }
}
