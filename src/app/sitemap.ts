import { MetadataRoute } from 'next'
import { getCompanies, getBenefits } from '@/lib/database'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://benefitlens.de'
  
  try {
    // Get all companies (limit to reasonable number for sitemap)
    const companies = await getCompanies({}, { limit: 1000 })
    
    // Get all benefits
    const benefits = await getBenefits()
    
    // Static pages
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
      {
        url: `${baseUrl}/companies`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/benefits`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
      {
        url: `${baseUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.6,
      },
      {
        url: `${baseUrl}/impressum`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
      {
        url: `${baseUrl}/datenschutz`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
      {
        url: `${baseUrl}/terms`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
    ]

    // Company pages
    const companyPages: MetadataRoute.Sitemap = companies.map((company) => ({
      url: `${baseUrl}/companies/${company.id}`,
      lastModified: new Date(company.updated_at || company.created_at),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    // Benefit filter pages (for major benefits)
    const benefitPages: MetadataRoute.Sitemap = benefits
      .slice(0, 50) // Limit to top 50 benefits to avoid too many URLs
      .map((benefit) => ({
        url: `${baseUrl}/companies?benefits=${encodeURIComponent(benefit.name)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.6,
      }))

    // Industry filter pages (get unique industries from companies)
    const industries = [...new Set(companies.map(c => c.industry).filter(Boolean))]
    const industryPages: MetadataRoute.Sitemap = industries
      .slice(0, 30) // Limit to top 30 industries
      .map((industry) => ({
        url: `${baseUrl}/companies?industry=${encodeURIComponent(industry)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.5,
      }))

    // Location filter pages (get unique cities from company locations)
    const cities = [...new Set(
      companies
        .flatMap(c => c.locations || [])
        .map(l => l.city)
        .filter(Boolean)
    )]
    const locationPages: MetadataRoute.Sitemap = cities
      .slice(0, 50) // Limit to top 50 cities
      .map((city) => ({
        url: `${baseUrl}/companies?location=${encodeURIComponent(city)}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.5,
      }))

    return [
      ...staticPages,
      ...companyPages,
      ...benefitPages,
      ...industryPages,
      ...locationPages,
    ]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    // Return minimal sitemap if there's an error
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
      {
        url: `${baseUrl}/companies`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/benefits`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
    ]
  }
}
