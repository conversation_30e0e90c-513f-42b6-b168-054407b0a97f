version: "3.9"

services:
  caddy:
    image: caddy:2
    container_name: edge-caddy
    restart: unless-stopped
    ports:
      - "80:80"      # HTTP (C<PERSON><PERSON> will redirect to HTTPS)
      - "443:443"    # HTTPS
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data     # stores certs/keys
      - caddy_config:/config
      - ./logs:/var/log/caddy
    networks:
      - edge

volumes:
  caddy_data:
  caddy_config:

networks:
  edge:
    external: true
