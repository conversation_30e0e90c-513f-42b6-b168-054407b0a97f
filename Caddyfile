# Your site(s). <PERSON><PERSON><PERSON> automatically redirects HTTP -> HTTPS.
benefitlens.de, www.benefitlens.de {
  # Optional: force www -> apex
  @www host www.benefitlens.de
  redir @www https://benefitlens.de{uri}

  encode gzip
  reverse_proxy benefitlens-app:3000  # resolves via shared "edge" network

  # Access log -> /var/log/caddy/benefitlens-access.log (inside container)
  log {
    output file /var/log/caddy/benefitlens-access.log {
        roll_size 50MiB       # smaller chunks
        roll_keep 7           # last 7 files
        roll_keep_for 168h    # 7 days
        # roll_uncompressed   # uncomment if you don't want gzip (not recommended)
        roll_local_time     # filenames use local time instead of UTC
      }
    format json
    level info
  }

  header {
    Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    X-Content-Type-Options "nosniff"
    Referrer-Policy "strict-origin-when-cross-origin"
  }
}

# Optional: expose Adminer over a subdomain with simple auth
# adminer.benefitlens.de {
#   encode gzip
#   basicauth {
#     # generate with: caddy hash-password
#     raul JDJhJDEwJHhv...hashed...string...
#   }
#   reverse_proxy benefitlens-adminer:8080
# }
